{"name": "gpt-load-frontend", "private": true, "version": "0.1.0", "description": "GPT Load Balancer Frontend - A modern Vue 3 frontend for GPT load balancing service", "type": "module", "keywords": ["vue3", "typescript", "vite", "naive-ui", "gpt-load", "frontend"], "author": "tbphp", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tbphp/gpt-load.git", "directory": "web"}, "bugs": {"url": "https://github.com/tbphp/gpt-load/issues"}, "homepage": "https://github.com/tbphp/gpt-load#readme", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint . --fix", "lint:check": "eslint .", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "vue-tsc --build --force", "clean": "rm -rf dist node_modules/.vite", "test:unit": "echo 'No tests specified' && exit 0", "prepare": "echo 'Prepare script for potential Git hooks'", "check-all": "npm lint:check && npm format:check && npm type-check"}, "dependencies": {"@vicons/ionicons5": "^0.13.0", "axios": "^1.9.0", "naive-ui": "^2.41.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^22.15.24", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.0.0", "@vue/eslint-config-typescript": "^14.0.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.30.1", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^9.0.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}