{"$schema": "https://json.schemastore.org/prettierrc", "semi": true, "singleQuote": false, "tabWidth": 2, "useTabs": false, "printWidth": 100, "endOfLine": "lf", "trailingComma": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "vueIndentScriptAndStyle": false, "htmlWhitespaceSensitivity": "ignore", "embeddedLanguageFormatting": "auto", "overrides": [{"files": ["*.vue"], "options": {"parser": "vue", "vueIndentScriptAndStyle": false}}, {"files": ["*.json", "*.jsonc"], "options": {"parser": "json", "trailingComma": "none"}}, {"files": ["*.md", "*.markdown"], "options": {"parser": "markdown", "printWidth": 80, "proseWrap": "preserve", "tabWidth": 2}}, {"files": ["*.yaml", "*.yml"], "options": {"parser": "yaml", "tabWidth": 2}}]}