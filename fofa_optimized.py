#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPT-Load FOFA搜索优化版本
基于FOFA实际支持的语法进行搜索
"""

import requests
import json
import base64
import time
from datetime import datetime

class FOFAOptimizedScanner:
    def __init__(self, fofa_url="https://fofa.red", api_key="1v43heo2ie8004lp"):
        self.fofa_url = fofa_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def search(self, query, size=50):
        """执行FOFA搜索"""
        try:
            encoded_query = base64.b64encode(query.encode()).decode()
            
            api_url = f"{self.fofa_url}/api/v1/search/all"
            params = {
                'key': self.api_key,
                'qbase64': encoded_query,
                'size': size,
                'fields': 'host,title,ip,port,protocol,country,region,city,server,banner'
            }
            
            response = self.session.get(api_url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('error'):
                    return {'success': False, 'error': data.get('errmsg')}
                
                return {
                    'success': True,
                    'query': query,
                    'total': data.get('size', 0),
                    'results': data.get('results', [])
                }
            else:
                return {'success': False, 'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def verify_site(self, url):
        """验证网站是否为gpt-load"""
        try:
            response = self.session.get(url, timeout=10, allow_redirects=True)
            content = response.text.lower()
            
            # 检查关键特征
            indicators = {
                'gpt_load_title': 'gpt-load' in content or 'gpt load' in content,
                'proxy_feature': '/proxy' in content,
                'ai_keywords': any(keyword in content for keyword in ['openai', 'gemini', 'claude', 'anthropic']),
                'vue_app': any(vue_indicator in content for vue_indicator in ['vue.js', '__vue__', 'vue-router']),
                'api_endpoints': '/api/v1' in content,
                'chinese_desc': '智能密钥' in response.text or 'AI代理' in response.text
            }
            
            confidence = sum(indicators.values()) / len(indicators) * 100
            return confidence >= 30, confidence, indicators
            
        except Exception as e:
            return False, 0, {'error': str(e)}

def get_optimized_queries():
    """获取优化的搜索语法（基于FOFA实际支持的功能）"""
    return {
        'title_based': [
            'title="GPT-Load"',
            'title="gpt-load"', 
            'title="GPT Load"',
            'title*="GPT" && title*="Load"',
            'title*="AI" && title*="代理"'
        ],
        
        'port_based': [
            'port="3001"',
            'port="3001" && country="CN"',
            'port="3001" && status_code="200"',
            'port="3001" && protocol="http"',
            'port="3001" && protocol="https"'
        ],
        
        'server_based': [
            'server="Go"',
            'server*="gin"',
            'server*="Go" && port="3001"',
            'header*="gin"'
        ],
        
        'path_based': [
            'path="/proxy"',
            'path="/api/v1"',
            'path="/api/v1/health"',
            'path*="/proxy/"'
        ],
        
        'banner_based': [
            'banner*="gpt-load"',
            'banner*="proxy"',
            'banner*="AI"'
        ],
        
        'combined_precise': [
            'title="GPT-Load" || title="gpt-load"',
            'port="3001" && title*="GPT"',
            'port="3001" && title*="Load"',
            'port="3001" && server*="Go"',
            'title*="AI" && port="3001"',
            'path="/proxy" && port="3001"',
            '(title="GPT-Load" || title="GPT Load") && country="CN"'
        ],
        
        'discovery_queries': [
            'port="3001" && country="CN" && status_code="200"',
            'title*="GPT" && port="3001" && country="CN"',
            'server*="Go" && title*="Load"',
            'path*="/api/" && port="3001"'
        ]
    }

def run_comprehensive_test():
    """运行全面测试"""
    scanner = FOFAOptimizedScanner()
    queries = get_optimized_queries()
    
    print("🚀 GPT-Load FOFA搜索全面测试")
    print("=" * 60)
    
    all_results = []
    
    for category, query_list in queries.items():
        print(f"\n📋 类别: {category}")
        print("-" * 40)
        
        for query in query_list:
            print(f"🔍 {query}")
            
            result = scanner.search(query, size=30)
            
            if result['success']:
                total = result['total']
                print(f"✅ {total} 个结果")
                
                all_results.append({
                    'category': category,
                    'query': query,
                    'total': total,
                    'results': result['results'][:5]  # 保存前5个结果
                })
                
                # 显示前3个结果
                for i, res in enumerate(result['results'][:3], 1):
                    if len(res) > 0:
                        host = res[0]
                        title = res[1] if len(res) > 1 else 'N/A'
                        country = res[5] if len(res) > 5 else 'N/A'
                        print(f"   {i}. {host} | {country} | {title[:40]}...")
            else:
                print(f"❌ {result['error']}")
            
            time.sleep(1.5)  # 避免请求过快
    
    return all_results

def analyze_and_verify_results(results):
    """分析和验证结果"""
    if not results:
        print("\n❌ 没有搜索结果")
        return
    
    print("\n" + "=" * 60)
    print("📊 搜索结果分析")
    print("=" * 60)
    
    # 按结果数量排序
    sorted_results = sorted(results, key=lambda x: x['total'], reverse=True)
    
    print(f"{'排名':<4} {'结果数':<8} {'类别':<15} {'搜索语法'}")
    print("-" * 80)
    
    for i, result in enumerate(sorted_results[:15], 1):
        category = result['category'][:14]
        total = result['total']
        query = result['query'][:40] + '...' if len(result['query']) > 40 else result['query']
        print(f"{i:<4} {total:<8} {category:<15} {query}")
    
    # 验证最佳结果
    if sorted_results:
        best_results = sorted_results[:3]  # 验证前3个最佳结果
        
        print(f"\n🔍 验证最佳搜索结果:")
        print("-" * 40)
        
        scanner = FOFAOptimizedScanner()
        verified_results = []
        
        for i, result in enumerate(best_results, 1):
            print(f"\n验证第{i}名: {result['query']} ({result['total']}个结果)")
            
            verified_count = 0
            total_checked = 0
            
            for res in result['results'][:3]:  # 验证前3个结果
                if len(res) > 0:
                    host = res[0]
                    protocol = res[4] if len(res) > 4 else 'http'
                    url = f"{protocol}://{host}"
                    
                    print(f"  检查: {url}")
                    is_gpt_load, confidence, indicators = scanner.verify_site(url)
                    
                    if is_gpt_load:
                        verified_count += 1
                        print(f"    ✅ GPT-Load (置信度: {confidence:.1f}%)")
                    else:
                        print(f"    ❌ 非GPT-Load (置信度: {confidence:.1f}%)")
                    
                    total_checked += 1
                    time.sleep(1)
            
            accuracy = (verified_count / total_checked * 100) if total_checked > 0 else 0
            verified_results.append({
                'query': result['query'],
                'total': result['total'],
                'accuracy': accuracy,
                'verified_count': verified_count,
                'checked_count': total_checked
            })
            
            print(f"  准确率: {verified_count}/{total_checked} ({accuracy:.1f}%)")
    
    # 生成最终推荐
    print(f"\n🏆 推荐搜索策略:")
    print("-" * 40)
    
    if verified_results:
        # 综合考虑结果数量和准确率
        for i, result in enumerate(verified_results, 1):
            score = result['total'] * (result['accuracy'] / 100)
            print(f"{i}. {result['query']}")
            print(f"   结果数: {result['total']} | 准确率: {result['accuracy']:.1f}% | 综合评分: {score:.1f}")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"gpt_load_fofa_results_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'total_queries': len(results),
            'sorted_results': sorted_results,
            'verified_results': verified_results if 'verified_results' in locals() else []
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细结果已保存到: {filename}")

def quick_discovery():
    """快速发现模式"""
    scanner = FOFAOptimizedScanner()
    
    # 最有效的搜索语法
    top_queries = [
        'title="GPT-Load" || title="gpt-load"',
        'port="3001" && country="CN"',
        'title*="GPT" && title*="Load"',
        'port="3001" && title*="GPT"',
        'server*="Go" && port="3001"'
    ]
    
    print("⚡ 快速发现模式")
    print("=" * 40)
    
    best_query = None
    max_results = 0
    
    for query in top_queries:
        print(f"\n🔍 {query}")
        result = scanner.search(query, size=20)
        
        if result['success']:
            total = result['total']
            print(f"✅ {total} 个结果")
            
            if total > max_results:
                max_results = total
                best_query = query
            
            # 显示前2个结果
            for i, res in enumerate(result['results'][:2], 1):
                if len(res) > 0:
                    host = res[0]
                    title = res[1] if len(res) > 1 else 'N/A'
                    print(f"   {i}. {host} - {title}")
        else:
            print(f"❌ {result['error']}")
        
        time.sleep(1)
    
    if best_query:
        print(f"\n🎯 推荐使用: {best_query}")
        print(f"📈 预期结果: {max_results} 个网站")

def main():
    """主函数"""
    print("GPT-Load FOFA搜索工具")
    print("选择运行模式:")
    print("1. 快速发现 (推荐)")
    print("2. 全面测试")
    
    choice = input("\n请选择 (1/2): ").strip()
    
    if choice == '1':
        quick_discovery()
    elif choice == '2':
        results = run_comprehensive_test()
        analyze_and_verify_results(results)
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
