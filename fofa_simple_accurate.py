#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPT-Load 简化精准搜索
基于已知真实站点的特征匹配
"""

import requests
import json
import base64
import time
from datetime import datetime
import re

class SimpleAccurateScanner:
    def __init__(self, fofa_url="https://fofa.red", api_key="1v43heo2ie8004lp"):
        self.fofa_url = fofa_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 已知真实GPT-Load特征
        self.gpt_load_signatures = {
            'titles': ['GPT Load', 'gpt-load', 'GPT-Load'],
            'cors_header': 'GET, POST, PUT, DELETE, OPTIONS',
            'api_endpoints': ['/api/v1/health', '/proxy/', '/api/v1/'],
            'response_codes': [200, 401, 403, 404]
        }
    
    def search_fofa(self, query, size=100):
        """执行FOFA搜索"""
        try:
            encoded_query = base64.b64encode(query.encode()).decode()
            
            api_url = f"{self.fofa_url}/api/v1/search/all"
            params = {
                'key': self.api_key,
                'qbase64': encoded_query,
                'size': size,
                'fields': 'host,title,ip,port,protocol,country,region,city,server,banner,header'
            }
            
            response = self.session.get(api_url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('error'):
                    return None
                
                return {
                    'query': query,
                    'total': data.get('size', 0),
                    'results': data.get('results', [])
                }
            else:
                return None
                
        except Exception as e:
            return None
    
    def simple_verify(self, url):
        """简化验证逻辑"""
        try:
            # 设置较短的超时时间
            resp = self.session.get(url, timeout=8, allow_redirects=True)
            
            score = 0
            features = []
            
            # 1. 检查标题
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', resp.text, re.IGNORECASE)
            if title_match:
                title = title_match.group(1).strip()
                if title in self.gpt_load_signatures['titles']:
                    score += 5
                    features.append(f"title:{title}")
                elif any(sig in title for sig in ['GPT', 'Load', 'AI']):
                    score += 2
                    features.append(f"title_partial:{title}")
            
            # 2. 检查CORS头（GPT-Load特有）
            cors_methods = resp.headers.get('Access-Control-Allow-Methods', '')
            if self.gpt_load_signatures['cors_header'] in cors_methods:
                score += 4
                features.append('cors_signature')
            
            # 3. 检查内容长度（GPT-Load默认页面通常很短）
            content_length = len(resp.text)
            if 400 <= content_length <= 600:  # GPT-Load默认页面大约470字节
                score += 2
                features.append('content_length_match')
            
            # 4. 检查状态码
            if resp.status_code == 200:
                score += 1
                features.append('status_200')
            
            # 5. 快速检查一个API端点
            try:
                api_url = url.rstrip('/') + '/api/v1/health'
                api_resp = self.session.get(api_url, timeout=3)
                if api_resp.status_code == 404:  # GPT-Load的health端点返回404
                    score += 3
                    features.append('health_404')
                elif api_resp.status_code in [401, 403]:
                    score += 1
                    features.append('api_auth_required')
            except:
                pass
            
            # 判断阈值：总分>=7认为是GPT-Load
            is_gpt_load = score >= 7
            
            return is_gpt_load, score, features
            
        except Exception as e:
            return False, 0, [f'error:{str(e)[:30]}']

def get_targeted_queries():
    """获取针对性搜索查询"""
    return [
        # 高精度查询
        'title="GPT Load"',
        'title="GPT-Load"',
        'title="gpt-load"',
        
        # 端口+标题组合
        'port="3001" && title="GPT"',
        'port="3001" && title="Load"',
        'port="3001" && title="AI"',
        
        # 技术栈
        'server="Go" && port="3001"',
        'header="gin" && port="3001"',
        
        # 地区过滤
        'port="3001" && country="CN" && title="GPT"',
        'port="3001" && country="US" && title="GPT"',
        
        # 其他端口
        'port="3000" && title="GPT"',
        'port="8080" && title="GPT"',
        
        # CORS特征
        'header="Access-Control-Allow-Methods"',
        
        # 域名模式
        'host="gpt-load"',
        'host="load"'
    ]

def run_targeted_scan():
    """运行针对性扫描"""
    scanner = SimpleAccurateScanner()
    queries = get_targeted_queries()
    
    print("🎯 GPT-Load 针对性精准扫描")
    print("=" * 40)
    
    verified_sites = []
    all_results = []
    checked_hosts = set()
    
    for i, query in enumerate(queries, 1):
        print(f"\n🔍 [{i}/{len(queries)}] {query}")
        
        result = scanner.search_fofa(query, size=30)
        
        if result and result['total'] > 0:
            print(f"✅ {result['total']} 个结果")
            all_results.append(result)
            
            verified_count = 0
            
            # 验证前8个结果
            for j, res in enumerate(result['results'][:8], 1):
                if len(res) > 0:
                    host = res[0]
                    title = res[1] if len(res) > 1 else 'N/A'
                    protocol = res[4] if len(res) > 4 else 'http'
                    country = res[5] if len(res) > 5 else 'N/A'
                    
                    # 避免重复检查
                    if host in checked_hosts:
                        continue
                    checked_hosts.add(host)
                    
                    # 修复URL格式问题
                    if host.startswith('https://https://'):
                        url = host.replace('https://https://', 'https://')
                    elif host.startswith('http://http://'):
                        url = host.replace('http://http://', 'http://')
                    else:
                        url = f"{protocol}://{host}"
                    
                    print(f"  {j}. {host} | {country} | {title[:20]}...")
                    
                    is_gpt_load, score, features = scanner.simple_verify(url)
                    
                    if is_gpt_load:
                        verified_count += 1
                        verified_sites.append({
                            'url': url,
                            'host': host,
                            'title': title,
                            'country': country,
                            'score': score,
                            'features': features,
                            'query': query
                        })
                        print(f"    ✅ GPT-Load确认 (评分: {score}) - {', '.join(features[:2])}")
                    else:
                        print(f"    ❌ 非GPT-Load (评分: {score}) - {', '.join(features[:2])}")
                    
                    time.sleep(0.8)  # 减少延迟
            
            print(f"  本轮验证: {verified_count} 个GPT-Load站点")
        else:
            print("❌ 无结果")
        
        time.sleep(1.5)
    
    return all_results, verified_sites

def generate_targeted_report(all_results, verified_sites):
    """生成针对性报告"""
    print("\n" + "=" * 50)
    print("📊 GPT-Load 针对性搜索报告")
    print("=" * 50)
    
    total_queries = len(all_results)
    total_results = sum(r['total'] for r in all_results)
    verified_count = len(verified_sites)
    
    print(f"\n📈 搜索统计:")
    print(f"  执行查询: {total_queries}")
    print(f"  搜索结果: {total_results}")
    print(f"  确认GPT-Load: {verified_count}")
    
    if verified_sites:
        print(f"\n✅ 确认的GPT-Load部署:")
        for i, site in enumerate(verified_sites, 1):
            print(f"  {i:2d}. {site['url']}")
            print(f"      标题: {site['title']}")
            print(f"      地区: {site['country']}")
            print(f"      评分: {site['score']}")
            print(f"      特征: {', '.join(site['features'])}")
            print(f"      发现语法: {site['query']}")
            print()
        
        # 分析最有效的搜索语法
        query_stats = {}
        for site in verified_sites:
            query = site['query']
            if query not in query_stats:
                query_stats[query] = 0
            query_stats[query] += 1
        
        sorted_queries = sorted(query_stats.items(), key=lambda x: x[1], reverse=True)
        
        print(f"🎯 最有效的搜索语法:")
        for query, count in sorted_queries:
            print(f"  {query} (发现 {count} 个)")
        
        # 地区分布
        country_stats = {}
        for site in verified_sites:
            country = site['country']
            if country not in country_stats:
                country_stats[country] = 0
            country_stats[country] += 1
        
        print(f"\n🌍 地区分布:")
        for country, count in sorted(country_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"  {country}: {count} 个")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total_queries': total_queries,
            'total_results': total_results,
            'verified_sites': verified_count
        },
        'verified_sites': verified_sites,
        'effective_queries': sorted_queries if verified_sites else []
    }
    
    filename = f"gpt_load_targeted_report_{timestamp}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 报告已保存: {filename}")
    
    # 生成最终建议
    print(f"\n💡 搜索建议:")
    if verified_sites:
        print(f"  🎯 推荐使用以下语法进行GPT-Load搜索:")
        for query, count in sorted_queries[:3]:
            print(f"    {query}")
        print(f"\n  📋 验证要点:")
        print(f"    - 检查页面标题是否为'GPT Load'")
        print(f"    - 检查CORS头是否包含'GET, POST, PUT, DELETE, OPTIONS'")
        print(f"    - 检查/api/v1/health端点是否返回404")
        print(f"    - 页面内容长度通常在400-600字节")
    else:
        print(f"  ❌ 未发现确认的GPT-Load部署")
        print(f"  💡 建议扩大搜索范围或调整验证逻辑")

def main():
    """主函数"""
    print("🚀 GPT-Load 简化精准搜索工具")
    print("基于已知特征的快速准确识别")
    
    all_results, verified_sites = run_targeted_scan()
    generate_targeted_report(all_results, verified_sites)

if __name__ == "__main__":
    main()
