#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPT-Load 精准搜索器
基于真实特征的准确识别
"""

import requests
import json
import base64
import time
from datetime import datetime
import re
from urllib.parse import urlparse, urljoin

class AccurateGPTLoadScanner:
    def __init__(self, fofa_url="https://fofa.red", api_key="1v43heo2ie8004lp"):
        self.fofa_url = fofa_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.session.timeout = 10
        
        # 已知的真实GPT-Load站点用于特征学习
        self.known_gpt_load_sites = [
            'http://**************:3001/',
            'https://gpt-load.110086.xyz/',
            'https://load.8388608.xyz',
            'http://**************:3001',
            'http://**************:3001'
        ]
    
    def learn_gpt_load_features(self):
        """从已知站点学习真实特征"""
        print("🔍 学习GPT-Load真实特征...")
        
        features = {
            'response_headers': set(),
            'status_codes': set(),
            'content_patterns': set(),
            'api_responses': set(),
            'page_titles': set(),
            'html_patterns': set()
        }
        
        for site in self.known_gpt_load_sites:
            try:
                print(f"  分析: {site}")
                
                # 检查主页
                resp = self.session.get(site, allow_redirects=True)
                
                # 收集响应头特征
                for header, value in resp.headers.items():
                    if header.lower() in ['server', 'x-powered-by', 'access-control-allow-methods']:
                        features['response_headers'].add(f"{header.lower()}:{value.lower()}")
                
                # 收集状态码
                features['status_codes'].add(resp.status_code)
                
                # 收集页面标题
                title_match = re.search(r'<title[^>]*>([^<]+)</title>', resp.text, re.IGNORECASE)
                if title_match:
                    features['page_titles'].add(title_match.group(1).strip())
                
                # 收集HTML特征
                content_lower = resp.text.lower()
                if 'vue' in content_lower:
                    features['html_patterns'].add('vue_app')
                if 'element' in content_lower:
                    features['html_patterns'].add('element_ui')
                if 'proxy' in content_lower:
                    features['html_patterns'].add('proxy_mention')
                
                # 检查API端点
                api_endpoints = ['/api/v1/health', '/proxy/', '/api/v1/', '/health']
                for endpoint in api_endpoints:
                    try:
                        api_url = urljoin(site, endpoint)
                        api_resp = self.session.get(api_url, timeout=5)
                        features['api_responses'].add(f"{endpoint}:{api_resp.status_code}")
                        
                        if api_resp.status_code in [401, 403]:
                            # 检查认证错误信息
                            if 'authorization' in api_resp.text.lower():
                                features['content_patterns'].add('auth_required')
                            if 'api key' in api_resp.text.lower():
                                features['content_patterns'].add('api_key_required')
                    except:
                        continue
                
                time.sleep(1)
                
            except Exception as e:
                print(f"    ❌ 分析失败: {str(e)}")
                continue
        
        print(f"✅ 特征学习完成")
        print(f"  响应头特征: {len(features['response_headers'])}")
        print(f"  页面标题: {len(features['page_titles'])}")
        print(f"  API响应: {len(features['api_responses'])}")
        print(f"  内容模式: {len(features['content_patterns'])}")
        
        return features
    
    def search_fofa(self, query, size=100):
        """执行FOFA搜索"""
        try:
            encoded_query = base64.b64encode(query.encode()).decode()
            
            api_url = f"{self.fofa_url}/api/v1/search/all"
            params = {
                'key': self.api_key,
                'qbase64': encoded_query,
                'size': size,
                'fields': 'host,title,ip,port,protocol,country,region,city,server,banner,header'
            }
            
            response = self.session.get(api_url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('error'):
                    return None
                
                return {
                    'query': query,
                    'total': data.get('size', 0),
                    'results': data.get('results', [])
                }
            else:
                return None
                
        except Exception as e:
            return None
    
    def accurate_verify(self, url, learned_features):
        """基于学习特征的精准验证"""
        try:
            print(f"    🔍 验证: {url}")
            
            # 检查主页
            resp = self.session.get(url, allow_redirects=True, timeout=10)
            
            score = 0
            matched_features = []
            
            # 1. 检查响应头特征
            for header, value in resp.headers.items():
                header_pattern = f"{header.lower()}:{value.lower()}"
                if header_pattern in learned_features['response_headers']:
                    score += 2
                    matched_features.append(f"header:{header}")
            
            # 2. 检查页面标题
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', resp.text, re.IGNORECASE)
            if title_match:
                title = title_match.group(1).strip()
                if title in learned_features['page_titles']:
                    score += 3
                    matched_features.append(f"title:{title}")
                elif any(known_title in title for known_title in learned_features['page_titles']):
                    score += 2
                    matched_features.append(f"title_partial:{title}")
            
            # 3. 检查HTML模式
            content_lower = resp.text.lower()
            for pattern in learned_features['html_patterns']:
                if pattern == 'vue_app' and 'vue' in content_lower:
                    score += 1
                    matched_features.append('vue_app')
                elif pattern == 'element_ui' and 'element' in content_lower:
                    score += 1
                    matched_features.append('element_ui')
                elif pattern == 'proxy_mention' and 'proxy' in content_lower:
                    score += 1
                    matched_features.append('proxy_mention')
            
            # 4. 检查API端点
            api_endpoints = ['/api/v1/health', '/proxy/', '/api/v1/', '/health']
            for endpoint in api_endpoints:
                try:
                    api_url = urljoin(url, endpoint)
                    api_resp = self.session.get(api_url, timeout=5)
                    
                    api_pattern = f"{endpoint}:{api_resp.status_code}"
                    if api_pattern in learned_features['api_responses']:
                        score += 2
                        matched_features.append(f"api:{endpoint}:{api_resp.status_code}")
                    
                    # 检查认证相关响应
                    if api_resp.status_code in [401, 403]:
                        if 'authorization' in api_resp.text.lower() or 'api key' in api_resp.text.lower():
                            score += 1
                            matched_features.append('auth_response')
                            
                except:
                    continue
            
            # 5. 特殊检查：GPT-Load特有的响应头
            cors_headers = resp.headers.get('Access-Control-Allow-Methods', '')
            if 'GET, POST, PUT, DELETE, OPTIONS' in cors_headers:
                score += 2
                matched_features.append('gpt_load_cors')
            
            # 判断是否为GPT-Load
            is_gpt_load = score >= 3  # 降低阈值，更宽松的判断
            
            print(f"      {'✅' if is_gpt_load else '❌'} 评分: {score} | 特征: {', '.join(matched_features[:3])}")
            
            return is_gpt_load, score, matched_features
            
        except Exception as e:
            print(f"      ❌ 验证异常: {str(e)}")
            return False, 0, []

def get_comprehensive_queries():
    """获取全面的搜索查询"""
    return [
        # 基础搜索
        'title="GPT-Load" || title="gpt-load"',
        'title="GPT Load"',
        
        # 端口+技术栈
        'port="3001"',
        'port="3001" && country="CN"',
        'port="3001" && country="US"',
        'server="Go" && port="3001"',
        
        # 框架特征
        'header="gin"',
        'server="gin"',
        
        # CORS特征 (GPT-Load特有)
        'header="Access-Control-Allow-Methods"',
        
        # 域名模式
        'domain="gpt-load"',
        'domain="load"',
        
        # 其他端口
        'port="3000" && title="GPT"',
        'port="8080" && title="GPT"',
        'port="80" && title="GPT Load"',
        'port="443" && title="GPT Load"',
        
        # 组合搜索
        'body="openai" && body="gemini"',
        'body="proxy" && port="3001"',
        'title="AI" && port="3001"'
    ]

def run_comprehensive_accurate_scan():
    """运行全面精准扫描"""
    scanner = AccurateGPTLoadScanner()
    
    print("🚀 GPT-Load 全面精准扫描")
    print("=" * 50)
    
    # 1. 学习真实特征
    learned_features = scanner.learn_gpt_load_features()
    
    # 2. 执行搜索
    queries = get_comprehensive_queries()
    all_results = []
    verified_sites = []
    unique_hosts = set()
    
    print(f"\n📋 开始搜索 ({len(queries)} 个查询)")
    print("-" * 30)
    
    for i, query in enumerate(queries, 1):
        print(f"\n🔍 [{i}/{len(queries)}] {query}")
        
        result = scanner.search_fofa(query, size=50)
        
        if result and result['total'] > 0:
            print(f"✅ {result['total']} 个结果")
            all_results.append(result)
            
            # 验证前10个结果
            for j, res in enumerate(result['results'][:10], 1):
                if len(res) > 0:
                    host = res[0]
                    title = res[1] if len(res) > 1 else 'N/A'
                    protocol = res[4] if len(res) > 4 else 'http'
                    country = res[5] if len(res) > 5 else 'N/A'
                    
                    if host not in unique_hosts:
                        unique_hosts.add(host)
                        url = f"{protocol}://{host}"
                        
                        print(f"  {j}. {host} | {country} | {title[:25]}...")
                        
                        is_gpt_load, score, features = scanner.accurate_verify(url, learned_features)
                        
                        if is_gpt_load:
                            verified_sites.append({
                                'url': url,
                                'host': host,
                                'title': title,
                                'country': country,
                                'score': score,
                                'features': features,
                                'query': query
                            })
                        
                        time.sleep(1)  # 避免请求过快
        else:
            print("❌ 无结果")
        
        time.sleep(2)
    
    return all_results, verified_sites, learned_features

def generate_accurate_report(all_results, verified_sites, learned_features):
    """生成精准报告"""
    print("\n" + "=" * 60)
    print("📊 GPT-Load 精准搜索报告")
    print("=" * 60)
    
    # 统计信息
    total_queries = len(all_results)
    total_results = sum(r['total'] for r in all_results)
    verified_count = len(verified_sites)
    
    print(f"\n📈 搜索统计:")
    print(f"  执行查询: {total_queries}")
    print(f"  搜索结果: {total_results}")
    print(f"  已验证GPT-Load: {verified_count}")
    
    # 学习到的特征
    print(f"\n🧠 学习到的GPT-Load特征:")
    print(f"  页面标题: {list(learned_features['page_titles'])}")
    print(f"  响应头: {list(learned_features['response_headers'])[:3]}")
    print(f"  API端点: {list(learned_features['api_responses'])}")
    
    # 已验证站点
    if verified_sites:
        print(f"\n✅ 确认的GPT-Load部署 ({verified_count} 个):")
        for i, site in enumerate(verified_sites, 1):
            print(f"  {i:2d}. {site['url']}")
            print(f"      标题: {site['title']}")
            print(f"      地区: {site['country']}")
            print(f"      评分: {site['score']}")
            print(f"      特征: {', '.join(site['features'][:3])}")
            print(f"      发现语法: {site['query']}")
            print()
    
    # 最有效的搜索语法
    if verified_sites:
        query_effectiveness = {}
        for site in verified_sites:
            query = site['query']
            if query not in query_effectiveness:
                query_effectiveness[query] = 0
            query_effectiveness[query] += 1
        
        sorted_queries = sorted(query_effectiveness.items(), key=lambda x: x[1], reverse=True)
        
        print(f"🎯 最有效的搜索语法:")
        for query, count in sorted_queries:
            print(f"  {query} (发现 {count} 个站点)")
    
    # 保存报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total_queries': total_queries,
            'total_results': total_results,
            'verified_sites': verified_count
        },
        'learned_features': {k: list(v) if isinstance(v, set) else v for k, v in learned_features.items()},
        'verified_sites': verified_sites,
        'effective_queries': sorted_queries if verified_sites else []
    }
    
    filename = f"gpt_load_accurate_report_{timestamp}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细报告已保存: {filename}")

def main():
    """主函数"""
    print("🎯 GPT-Load 精准搜索工具")
    print("基于真实站点特征学习的准确识别")
    
    all_results, verified_sites, learned_features = run_comprehensive_accurate_scan()
    generate_accurate_report(all_results, verified_sites, learned_features)

if __name__ == "__main__":
    main()
