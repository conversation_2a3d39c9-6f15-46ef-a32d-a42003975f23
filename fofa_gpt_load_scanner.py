#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPT-Load项目FOFA搜索语法测试工具
用于测试不同的FOFA搜索语法组合，找到部署gpt-load项目的网站
"""

import requests
import json
import time
import base64
from urllib.parse import quote
import sys

class FOFAScanner:
    def __init__(self, fofa_url, api_key):
        self.fofa_url = fofa_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def search(self, query, size=100, page=1):
        """执行FOFA搜索"""
        try:
            # Base64编码查询语句
            encoded_query = base64.b64encode(query.encode()).decode()
            
            # 构建API请求URL
            api_url = f"{self.fofa_url}/api/v1/search/all"
            params = {
                'key': self.api_key,
                'qbase64': encoded_query,
                'size': size,
                'page': page,
                'fields': 'host,title,ip,port,protocol,country,region,city,server,banner'
            }
            
            print(f"🔍 搜索语法: {query}")
            print(f"📡 请求URL: {api_url}")
            
            response = self.session.get(api_url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('error'):
                    print(f"❌ API错误: {data.get('errmsg', '未知错误')}")
                    return None
                
                results = data.get('results', [])
                total = data.get('size', 0)
                
                print(f"✅ 搜索成功，共找到 {total} 个结果")
                return {
                    'query': query,
                    'total': total,
                    'results': results,
                    'success': True
                }
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 搜索异常: {str(e)}")
            return None
    
    def analyze_results(self, search_result):
        """分析搜索结果"""
        if not search_result or not search_result.get('success'):
            return
        
        results = search_result.get('results', [])
        if not results:
            print("📊 无结果数据")
            return
        
        print(f"\n📊 结果分析 (前10条):")
        print("-" * 80)
        
        for i, result in enumerate(results[:10], 1):
            host = result[0] if len(result) > 0 else 'N/A'
            title = result[1] if len(result) > 1 else 'N/A'
            ip = result[2] if len(result) > 2 else 'N/A'
            port = result[3] if len(result) > 3 else 'N/A'
            protocol = result[4] if len(result) > 4 else 'N/A'
            country = result[5] if len(result) > 5 else 'N/A'
            server = result[7] if len(result) > 7 else 'N/A'
            
            print(f"{i:2d}. {protocol}://{host}")
            print(f"    IP: {ip}:{port} | 国家: {country}")
            print(f"    标题: {title}")
            print(f"    服务器: {server}")
            print()

def main():
    # FOFA配置
    FOFA_URL = "https://fofa.red"
    API_KEY = "1v43heo2ie8004lp"
    
    scanner = FOFAScanner(FOFA_URL, API_KEY)
    
    # 定义多种搜索语法策略
    search_strategies = [
        # 基础端口搜索
        {
            'name': '端口特征搜索',
            'queries': [
                'port="3001"',
                'port="3001" && country="CN"',
                'port="3001" && status_code="200"'
            ]
        },
        
        # 标题和内容特征
        {
            'name': '标题内容特征',
            'queries': [
                'title="GPT-Load"',
                'title*="GPT" && title*="Load"',
                'title*="AI" && title*="代理"',
                'body*="gpt-load"',
                'body*="智能密钥轮询"'
            ]
        },
        
        # 路径和API特征
        {
            'name': 'API路径特征',
            'queries': [
                'path="/proxy"',
                'path*="/proxy/"',
                'body*="/proxy/" && port="3001"',
                'path="/api/v1" && port="3001"'
            ]
        },
        
        # Vue.js应用特征
        {
            'name': 'Vue.js应用特征',
            'queries': [
                'body*="Vue" && port="3001"',
                'body*="vue.js" && body*="proxy"',
                'header*="X-Powered-By" && body*="vue"'
            ]
        },
        
        # 服务器和技术栈特征
        {
            'name': '技术栈特征',
            'queries': [
                'server*="Go" && port="3001"',
                'header*="gin" || body*="gin"',
                'body*="golang" && body*="proxy"'
            ]
        },
        
        # 组合搜索策略
        {
            'name': '组合搜索策略',
            'queries': [
                'port="3001" && (title*="GPT" || body*="gpt-load")',
                'port="3001" && body*="proxy" && body*="AI"',
                '(title*="GPT-Load" || body*="gpt-load") && country="CN"',
                'port="3001" && (body*="/proxy/" || path*="/proxy")',
                'body*="智能密钥" || body*="Multi-channel AI proxy"'
            ]
        },

        # 高级精确搜索
        {
            'name': '高级精确搜索',
            'queries': generate_advanced_queries()[:8]  # 取前8个高级查询
        }
    ]
    
    print("🚀 开始GPT-Load项目FOFA搜索测试")
    print("=" * 60)
    
    all_results = []
    
    for strategy in search_strategies:
        print(f"\n📋 策略: {strategy['name']}")
        print("-" * 40)
        
        for query in strategy['queries']:
            result = scanner.search(query, size=50)
            if result:
                all_results.append(result)
                scanner.analyze_results(result)
            
            # 避免请求过快
            time.sleep(2)
            print()
    
    # 生成搜索报告
    print("\n" + "=" * 60)
    print("📈 搜索效果统计报告")
    print("=" * 60)
    
    # 按结果数量排序
    all_results.sort(key=lambda x: x.get('total', 0), reverse=True)
    
    print(f"{'排名':<4} {'结果数':<8} {'搜索语法'}")
    print("-" * 80)
    
    for i, result in enumerate(all_results[:15], 1):
        total = result.get('total', 0)
        query = result.get('query', '')
        print(f"{i:<4} {total:<8} {query}")
    
    # 推荐最佳搜索语法
    if all_results:
        best_result = all_results[0]
        print(f"\n🏆 推荐搜索语法:")
        print(f"   {best_result.get('query')}")
        print(f"   (找到 {best_result.get('total', 0)} 个结果)")

        # 验证前几个结果
        print(f"\n🔍 验证搜索结果准确性:")
        verified_count = 0
        results_to_verify = best_result.get('results', [])[:5]  # 验证前5个结果

        for i, result in enumerate(results_to_verify, 1):
            if len(result) > 0:
                host = result[0]
                protocol = result[4] if len(result) > 4 else 'http'
                url = f"{protocol}://{host}"

                print(f"\n验证 {i}/{len(results_to_verify)}: {url}")
                if scanner.verify_gpt_load_site(url):
                    verified_count += 1
                    print("   ✅ 确认为GPT-Load部署")
                else:
                    print("   ❌ 非GPT-Load部署")

        accuracy = (verified_count / len(results_to_verify) * 100) if results_to_verify else 0
        print(f"\n📊 验证准确率: {verified_count}/{len(results_to_verify)} ({accuracy:.1f}%)")

        # 生成优化建议
        print(f"\n💡 搜索优化建议:")
        if best_result.get('total', 0) > 1000:
            print("   - 结果过多，建议添加地区限制: && country=\"CN\"")
            print("   - 或添加状态码过滤: && status_code=\"200\"")
        elif best_result.get('total', 0) < 10:
            print("   - 结果较少，建议放宽搜索条件")
            print("   - 可以尝试模糊匹配: title*=\"关键词\"")
        else:
            print("   - 搜索结果数量适中，建议使用此语法")

        if accuracy < 50:
            print("   - 准确率较低，建议使用更精确的搜索条件")
        elif accuracy > 80:
            print("   - 准确率很高，推荐使用此搜索语法")


    def verify_gpt_load_site(self, url):
        """验证网站是否为gpt-load部署"""
        try:
            print(f"🔍 验证网站: {url}")

            # 检查主页
            response = self.session.get(url, timeout=10, allow_redirects=True)

            indicators = {
                'title_match': False,
                'body_content': False,
                'proxy_path': False,
                'vue_app': False,
                'api_endpoint': False
            }

            # 检查标题
            if 'gpt-load' in response.text.lower() or 'gpt load' in response.text.lower():
                indicators['title_match'] = True

            # 检查页面内容
            content_keywords = ['智能密钥', 'AI代理', 'proxy', 'Multi-channel', 'key rotation']
            if any(keyword.lower() in response.text.lower() for keyword in content_keywords):
                indicators['body_content'] = True

            # 检查Vue应用特征
            vue_indicators = ['vue.js', '__vue__', 'vue-router', 'element-plus']
            if any(indicator in response.text.lower() for indicator in vue_indicators):
                indicators['vue_app'] = True

            # 检查API端点
            try:
                api_response = self.session.get(f"{url}/api/v1/health", timeout=5)
                if api_response.status_code in [200, 401, 403]:
                    indicators['api_endpoint'] = True
            except:
                pass

            # 检查proxy路径
            try:
                proxy_response = self.session.get(f"{url}/proxy", timeout=5)
                if proxy_response.status_code in [200, 401, 403, 404]:
                    indicators['proxy_path'] = True
            except:
                pass

            confidence = sum(indicators.values()) / len(indicators) * 100

            print(f"   标题匹配: {'✅' if indicators['title_match'] else '❌'}")
            print(f"   内容特征: {'✅' if indicators['body_content'] else '❌'}")
            print(f"   Vue应用: {'✅' if indicators['vue_app'] else '❌'}")
            print(f"   API端点: {'✅' if indicators['api_endpoint'] else '❌'}")
            print(f"   Proxy路径: {'✅' if indicators['proxy_path'] else '❌'}")
            print(f"   置信度: {confidence:.1f}%")

            return confidence >= 40  # 40%以上置信度认为是gpt-load

        except Exception as e:
            print(f"   验证失败: {str(e)}")
            return False

def generate_advanced_queries():
    """生成高级搜索语法"""
    return [
        # 精确特征搜索
        'title="GPT-Load" || title="gpt-load"',
        'body*="tbphp/gpt-load"',
        'body*="智能密钥轮询的多渠道 AI 代理"',
        'body*="Multi-channel AI proxy with intelligent key rotation"',

        # 技术栈组合
        'port="3001" && body*="Vue" && body*="proxy"',
        'server*="Go" && body*="AI" && body*="proxy"',
        'body*="gin" && body*="openai" && port="3001"',

        # API特征
        'path="/api/v1/health" && port="3001"',
        'path="/proxy/" && body*="AI"',
        'body*="/proxy/" && body*="chat/completions"',

        # 错误页面特征
        'body*="Authorization required" && port="3001"',
        'body*="Invalid API key" && body*="proxy"',

        # 管理界面特征
        'body*="管理控制台" && body*="密钥管理"',
        'body*="Dashboard" && body*="API Keys" && port="3001"',

        # 配置文件泄露
        'path="/.env" && body*="AUTH_KEY"',
        'path="/docker-compose.yml" && body*="gpt-load"',

        # 组合高精度搜索
        '(title*="GPT" && title*="Load") || (body*="gpt-load" && port="3001")',
        'port="3001" && (body*="智能密钥" || body*="key rotation") && country="CN"',
        'body*="proxy" && body*="openai" && body*="gemini" && body*="claude"'
    ]

if __name__ == "__main__":
    main()
