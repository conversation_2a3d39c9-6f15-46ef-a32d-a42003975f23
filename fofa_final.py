#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPT-Load FOFA搜索最终版本
基于FOFA免费版限制优化的搜索策略
"""

import requests
import json
import base64
import time
from datetime import datetime

class GPTLoadFOFAScanner:
    def __init__(self, fofa_url="https://fofa.red", api_key="1v43heo2ie8004lp"):
        self.fofa_url = fofa_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def search(self, query, size=50):
        """执行FOFA搜索"""
        try:
            encoded_query = base64.b64encode(query.encode()).decode()
            
            api_url = f"{self.fofa_url}/api/v1/search/all"
            params = {
                'key': self.api_key,
                'qbase64': encoded_query,
                'size': size,
                'fields': 'host,title,ip,port,protocol,country,region,city,server'
            }
            
            print(f"🔍 搜索: {query}")
            response = self.session.get(api_url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('error'):
                    print(f"❌ {data.get('errmsg')}")
                    return None
                
                total = data.get('size', 0)
                results = data.get('results', [])
                print(f"✅ 找到 {total} 个结果")
                
                return {
                    'query': query,
                    'total': total,
                    'results': results
                }
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 搜索异常: {str(e)}")
            return None
    
    def verify_gpt_load(self, url):
        """验证是否为GPT-Load部署"""
        try:
            print(f"  🔍 验证: {url}")
            response = self.session.get(url, timeout=8, allow_redirects=True)
            content = response.text.lower()
            
            # GPT-Load特征检测
            features = {
                'title_match': any(keyword in content for keyword in ['gpt-load', 'gpt load']),
                'proxy_api': '/proxy' in content,
                'ai_services': any(service in content for service in ['openai', 'gemini', 'claude']),
                'management': any(term in content for term in ['管理', 'dashboard', 'api key']),
                'vue_app': any(vue in content for vue in ['vue.js', '__vue__', 'element']),
                'chinese_desc': any(desc in response.text for desc in ['智能密钥', 'AI代理', '多渠道'])
            }
            
            score = sum(features.values())
            confidence = score / len(features) * 100
            
            is_gpt_load = score >= 2  # 至少匹配2个特征
            
            print(f"    {'✅' if is_gpt_load else '❌'} 置信度: {confidence:.1f}% (特征: {score}/{len(features)})")
            
            if is_gpt_load:
                matched_features = [k for k, v in features.items() if v]
                print(f"    匹配特征: {', '.join(matched_features)}")
            
            return is_gpt_load, confidence, features
            
        except Exception as e:
            print(f"    ❌ 验证失败: {str(e)}")
            return False, 0, {}

def get_free_tier_queries():
    """获取适用于FOFA免费版的搜索语法"""
    return {
        'precise_title': [
            'title="GPT-Load"',
            'title="gpt-load"',
            'title="GPT Load"',
            'title="AI代理"'
        ],
        
        'port_location': [
            'port="3001"',
            'port="3001" && country="CN"',
            'port="3001" && region="Beijing"',
            'port="3001" && region="Shanghai"',
            'port="3001" && region="Guangdong"'
        ],
        
        'combined_basic': [
            'title="GPT-Load" || title="gpt-load"',
            'title="GPT Load" && country="CN"',
            'port="3001" && title="GPT-Load"',
            'port="3001" && title="gpt-load"'
        ],
        
        'server_tech': [
            'server="Go"',
            'server="gin"',
            'header="gin"'
        ],
        
        'path_based': [
            'path="/proxy"',
            'path="/api/v1"',
            'path="/health"'
        ]
    }

def run_targeted_search():
    """运行针对性搜索"""
    scanner = GPTLoadFOFAScanner()
    queries = get_free_tier_queries()
    
    print("🎯 GPT-Load 针对性搜索")
    print("=" * 50)
    
    all_results = []
    verified_sites = []
    
    for category, query_list in queries.items():
        print(f"\n📋 {category}")
        print("-" * 30)
        
        for query in query_list:
            result = scanner.search(query, size=20)
            
            if result and result['total'] > 0:
                all_results.append(result)
                
                # 显示并验证前几个结果
                print(f"📊 前5个结果:")
                for i, res in enumerate(result['results'][:5], 1):
                    if len(res) > 0:
                        host = res[0]
                        title = res[1] if len(res) > 1 else 'N/A'
                        country = res[5] if len(res) > 5 else 'N/A'
                        protocol = res[4] if len(res) > 4 else 'http'
                        
                        print(f"  {i}. {host} | {country} | {title}")
                        
                        # 验证前3个结果
                        if i <= 3:
                            url = f"{protocol}://{host}"
                            is_gpt_load, confidence, features = scanner.verify_gpt_load(url)
                            
                            if is_gpt_load:
                                verified_sites.append({
                                    'url': url,
                                    'title': title,
                                    'country': country,
                                    'confidence': confidence,
                                    'query': query,
                                    'features': features
                                })
                            
                            time.sleep(1)  # 避免请求过快
            
            time.sleep(2)  # 搜索间隔
    
    return all_results, verified_sites

def generate_final_report(all_results, verified_sites):
    """生成最终报告"""
    print("\n" + "=" * 60)
    print("📈 GPT-Load FOFA搜索最终报告")
    print("=" * 60)
    
    # 搜索效果统计
    print(f"\n📊 搜索统计:")
    print(f"  总查询数: {len(all_results)}")
    print(f"  有效查询: {len([r for r in all_results if r['total'] > 0])}")
    print(f"  总结果数: {sum(r['total'] for r in all_results)}")
    print(f"  验证站点: {len(verified_sites)}")
    
    # 最佳搜索语法
    if all_results:
        sorted_results = sorted(all_results, key=lambda x: x['total'], reverse=True)
        
        print(f"\n🏆 最佳搜索语法 (按结果数排序):")
        for i, result in enumerate(sorted_results[:5], 1):
            print(f"  {i}. {result['query']} ({result['total']} 个结果)")
    
    # 已验证的GPT-Load站点
    if verified_sites:
        print(f"\n✅ 已确认的GPT-Load部署站点:")
        for i, site in enumerate(verified_sites, 1):
            print(f"  {i}. {site['url']}")
            print(f"     标题: {site['title']}")
            print(f"     地区: {site['country']}")
            print(f"     置信度: {site['confidence']:.1f}%")
            print(f"     发现语法: {site['query']}")
            print()
    
    # 推荐搜索策略
    print(f"\n💡 推荐FOFA搜索策略:")
    
    # 基于验证结果推荐
    if verified_sites:
        successful_queries = list(set(site['query'] for site in verified_sites))
        print(f"  🎯 高精度搜索 (已验证有效):")
        for query in successful_queries[:3]:
            print(f"    {query}")
    
    # 基于结果数量推荐
    if all_results:
        high_volume_queries = [r['query'] for r in sorted_results[:3] if r['total'] < 10000]
        print(f"  📈 高覆盖搜索 (结果数适中):")
        for query in high_volume_queries:
            print(f"    {query}")
    
    # 组合搜索建议
    print(f"  🔄 组合搜索建议:")
    print(f"    1. 先用精确搜索: title=\"GPT-Load\" || title=\"gpt-load\"")
    print(f"    2. 再用地区过滤: port=\"3001\" && country=\"CN\"")
    print(f"    3. 最后验证结果的真实性")
    
    # 保存报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total_queries': len(all_results),
            'total_results': sum(r['total'] for r in all_results),
            'verified_sites': len(verified_sites)
        },
        'search_results': all_results,
        'verified_sites': verified_sites,
        'recommendations': {
            'high_precision': successful_queries if verified_sites else [],
            'high_coverage': [r['query'] for r in sorted_results[:3]] if all_results else []
        }
    }
    
    filename = f"gpt_load_fofa_final_report_{timestamp}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 完整报告已保存: {filename}")

def quick_scan():
    """快速扫描模式"""
    scanner = GPTLoadFOFAScanner()
    
    # 最有效的搜索语法
    best_queries = [
        'title="GPT-Load" || title="gpt-load"',
        'port="3001" && country="CN"',
        'title="GPT Load"'
    ]
    
    print("⚡ 快速扫描模式")
    print("=" * 30)
    
    for query in best_queries:
        result = scanner.search(query, size=10)
        
        if result and result['total'] > 0:
            print(f"\n📋 发现 {result['total']} 个潜在目标")
            
            for i, res in enumerate(result['results'][:3], 1):
                if len(res) > 0:
                    host = res[0]
                    title = res[1] if len(res) > 1 else 'N/A'
                    protocol = res[4] if len(res) > 4 else 'http'
                    
                    print(f"  {i}. {protocol}://{host}")
                    print(f"     {title}")
        
        time.sleep(2)

def main():
    """主函数"""
    print("🚀 GPT-Load FOFA搜索工具")
    print("基于FOFA免费版优化")
    print("\n选择模式:")
    print("1. 快速扫描")
    print("2. 完整搜索+验证")
    
    choice = input("\n请选择 (1/2): ").strip()
    
    if choice == '1':
        quick_scan()
    elif choice == '2':
        all_results, verified_sites = run_targeted_search()
        generate_final_report(all_results, verified_sites)
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
