#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPT-Load FOFA最优搜索策略
基于实际测试结果的精准搜索方法
"""

import requests
import json
import base64
import time
from datetime import datetime

class OptimizedGPTLoadScanner:
    def __init__(self, fofa_url="https://fofa.red", api_key="1v43heo2ie8004lp"):
        self.fofa_url = fofa_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def search(self, query, size=100):
        """执行FOFA搜索"""
        try:
            encoded_query = base64.b64encode(query.encode()).decode()
            
            api_url = f"{self.fofa_url}/api/v1/search/all"
            params = {
                'key': self.api_key,
                'qbase64': encoded_query,
                'size': size,
                'fields': 'host,title,ip,port,protocol,country,region,city,server,banner'
            }
            
            response = self.session.get(api_url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('error'):
                    return None
                
                return {
                    'query': query,
                    'total': data.get('size', 0),
                    'results': data.get('results', [])
                }
            else:
                return None
                
        except Exception as e:
            return None
    
    def quick_verify(self, url):
        """快速验证GPT-Load特征"""
        try:
            response = self.session.get(url, timeout=8, allow_redirects=True)
            content = response.text.lower()
            
            # 关键特征检测
            gpt_load_indicators = [
                'gpt-load' in content,
                'gpt load' in content,
                '/proxy/' in content,
                'openai' in content and 'gemini' in content,
                'ai代理' in response.text,
                '智能密钥' in response.text,
                'vue.js' in content and 'proxy' in content
            ]
            
            score = sum(gpt_load_indicators)
            return score >= 2, score  # 至少匹配2个特征
            
        except:
            return False, 0

def get_optimized_queries():
    """基于测试结果的最优搜索语法"""
    return {
        # 高精度搜索 (结果少但准确率高)
        'high_precision': [
            'title="GPT-Load" || title="gpt-load"',  # 21个结果，准确率高
            'server="Go" && port="3001"',            # 9个结果，技术栈匹配
            'header="gin"',                          # 3421个结果，框架特征
            'title="AI代理"',                        # 62个结果，中文标题
        ],
        
        # 中等精度搜索 (平衡结果数量和准确率)
        'medium_precision': [
            'port="3001" && title="GPT"',
            'port="3001" && title="AI"', 
            'port="3001" && title="Load"',
            'server="gin"',                          # 61个结果
            'body="openai" && port="3001"',
            'body="gemini" && port="3001"',
            'body="claude" && port="3001"'
        ],
        
        # 地区过滤搜索 (针对特定地区)
        'regional_search': [
            'port="3001" && country="CN" && title="GPT"',
            'port="3001" && country="CN" && title="AI"',
            'port="3001" && country="US" && title="GPT"',
            'port="3001" && region="Beijing"',
            'port="3001" && region="Shanghai"',
            'port="3001" && region="Guangdong"'
        ],
        
        # 技术栈组合搜索
        'tech_stack': [
            'server="Go" && body="proxy"',
            'server="Go" && body="API"',
            'header="gin" && port="3001"',
            'body="vue.js" && port="3001"',
            'body="element" && port="3001"'
        ],
        
        # 错误页面搜索 (可能暴露GPT-Load)
        'error_based': [
            'body="Invalid API key" && port="3001"',
            'body="Authorization required" && port="3001"',
            'body="Unauthorized" && port="3001"'
        ]
    }

def run_optimized_search():
    """运行优化搜索"""
    scanner = OptimizedGPTLoadScanner()
    queries = get_optimized_queries()
    
    print("🎯 GPT-Load 优化搜索策略")
    print("基于实际测试结果的精准搜索")
    print("=" * 50)
    
    all_results = []
    verified_sites = []
    unique_hosts = set()
    
    for category, query_list in queries.items():
        print(f"\n📋 {category}")
        print("-" * 30)
        
        for query in query_list:
            print(f"🔍 {query}")
            
            result = scanner.search(query, size=30)
            
            if result and result['total'] > 0:
                print(f"✅ {result['total']} 个结果")
                all_results.append(result)
                
                # 验证前5个结果
                verified_count = 0
                for i, res in enumerate(result['results'][:5], 1):
                    if len(res) > 0:
                        host = res[0]
                        title = res[1] if len(res) > 1 else 'N/A'
                        protocol = res[4] if len(res) > 4 else 'http'
                        country = res[5] if len(res) > 5 else 'N/A'
                        
                        url = f"{protocol}://{host}"
                        
                        print(f"  {i}. {host} | {country} | {title[:30]}...")
                        
                        # 快速验证
                        if host not in unique_hosts:
                            unique_hosts.add(host)
                            is_gpt_load, score = scanner.quick_verify(url)
                            
                            if is_gpt_load:
                                verified_count += 1
                                verified_sites.append({
                                    'url': url,
                                    'host': host,
                                    'title': title,
                                    'country': country,
                                    'score': score,
                                    'query': query
                                })
                                print(f"    ✅ 确认GPT-Load (特征: {score}/7)")
                            else:
                                print(f"    ❌ 非GPT-Load (特征: {score}/7)")
                            
                            time.sleep(1)  # 避免请求过快
                
                print(f"  验证结果: {verified_count}/5")
            else:
                print("❌ 无结果")
            
            time.sleep(2)
    
    return all_results, verified_sites

def generate_final_recommendations(all_results, verified_sites):
    """生成最终推荐"""
    print("\n" + "=" * 50)
    print("📊 GPT-Load 搜索结果总结")
    print("=" * 50)
    
    # 统计信息
    total_queries = len(all_results)
    total_results = sum(r['total'] for r in all_results)
    verified_count = len(verified_sites)
    
    print(f"\n📈 搜索统计:")
    print(f"  执行查询: {total_queries}")
    print(f"  搜索结果: {total_results}")
    print(f"  已验证GPT-Load: {verified_count}")
    
    # 最佳搜索语法
    if all_results:
        # 按结果数量排序，但过滤掉结果过多的
        filtered_results = [r for r in all_results if r['total'] < 10000]
        sorted_results = sorted(filtered_results, key=lambda x: x['total'], reverse=True)
        
        print(f"\n🏆 推荐搜索语法 (结果数适中):")
        for i, result in enumerate(sorted_results[:8], 1):
            print(f"  {i}. {result['query']} ({result['total']} 个结果)")
    
    # 已验证站点
    if verified_sites:
        print(f"\n✅ 已确认的GPT-Load部署:")
        for i, site in enumerate(verified_sites, 1):
            print(f"  {i}. {site['url']}")
            print(f"     标题: {site['title']}")
            print(f"     地区: {site['country']}")
            print(f"     特征匹配: {site['score']}/7")
            print(f"     发现语法: {site['query']}")
            print()
    
    # 搜索策略建议
    print(f"💡 最佳搜索策略:")
    
    if verified_count > 0:
        # 分析成功的搜索语法
        successful_queries = list(set(site['query'] for site in verified_sites))
        print(f"  🎯 高效搜索语法:")
        for query in successful_queries:
            count = len([s for s in verified_sites if s['query'] == query])
            print(f"    {query} (发现 {count} 个站点)")
    
    print(f"\n  📋 综合搜索建议:")
    print(f"    1. 优先使用: title=\"GPT-Load\" || title=\"gpt-load\"")
    print(f"    2. 技术栈过滤: server=\"Go\" && port=\"3001\"")
    print(f"    3. 框架特征: header=\"gin\"")
    print(f"    4. 地区限制: port=\"3001\" && country=\"CN\"")
    print(f"    5. 组合搜索: port=\"3001\" && title=\"GPT\"")
    
    print(f"\n  ⚠️  搜索注意事项:")
    print(f"    - 避免使用结果过多的语法 (>10000)")
    print(f"    - 结合多个搜索语法提高覆盖率")
    print(f"    - 重点关注3001端口 + Go服务器的组合")
    print(f"    - 验证时检查/proxy/路径和AI服务特征")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total_queries': total_queries,
            'total_results': total_results,
            'verified_sites': verified_count
        },
        'verified_sites': verified_sites,
        'recommended_queries': [r['query'] for r in sorted_results[:5]] if all_results else [],
        'successful_queries': list(set(site['query'] for site in verified_sites)) if verified_sites else []
    }
    
    filename = f"gpt_load_optimized_results_{timestamp}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细结果已保存: {filename}")

def quick_scan_mode():
    """快速扫描模式 - 只使用最有效的搜索语法"""
    scanner = OptimizedGPTLoadScanner()
    
    # 最有效的搜索语法
    best_queries = [
        'title="GPT-Load" || title="gpt-load"',
        'server="Go" && port="3001"',
        'header="gin"',
        'port="3001" && title="GPT"'
    ]
    
    print("⚡ 快速扫描模式")
    print("=" * 30)
    
    verified_sites = []
    
    for query in best_queries:
        print(f"\n🔍 {query}")
        result = scanner.search(query, size=20)
        
        if result and result['total'] > 0:
            print(f"✅ {result['total']} 个结果")
            
            for i, res in enumerate(result['results'][:3], 1):
                if len(res) > 0:
                    host = res[0]
                    title = res[1] if len(res) > 1 else 'N/A'
                    protocol = res[4] if len(res) > 4 else 'http'
                    
                    url = f"{protocol}://{host}"
                    print(f"  {i}. {url}")
                    print(f"     {title}")
                    
                    # 快速验证
                    is_gpt_load, score = scanner.quick_verify(url)
                    if is_gpt_load:
                        verified_sites.append(url)
                        print(f"     ✅ GPT-Load (特征: {score}/7)")
                    else:
                        print(f"     ❌ 非GPT-Load (特征: {score}/7)")
                    
                    time.sleep(1)
        else:
            print("❌ 无结果")
        
        time.sleep(2)
    
    if verified_sites:
        print(f"\n🎯 发现的GPT-Load站点:")
        for i, url in enumerate(verified_sites, 1):
            print(f"  {i}. {url}")

def main():
    """主函数"""
    print("🚀 GPT-Load FOFA优化搜索工具")
    print("基于实际测试的最佳搜索策略")
    print("\n选择模式:")
    print("1. 快速扫描 (推荐)")
    print("2. 完整搜索")
    
    choice = input("\n请选择 (1/2): ").strip()
    
    if choice == '1':
        quick_scan_mode()
    elif choice == '2':
        all_results, verified_sites = run_optimized_search()
        generate_final_recommendations(all_results, verified_sites)
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
