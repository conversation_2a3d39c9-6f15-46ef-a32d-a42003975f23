{"fofa": {"url": "https://fofa.red", "api_key": "1v43heo2ie8004lp", "default_size": 50, "request_delay": 2}, "gpt_load_indicators": {"title_keywords": ["GPT-Load", "gpt-load", "AI代理", "智能密钥"], "body_keywords": ["tbphp/gpt-load", "智能密钥轮询的多渠道 AI 代理", "Multi-channel AI proxy with intelligent key rotation", "gpt-load.com", "/proxy/", "chat/completions", "openai", "gemini", "claude"], "path_indicators": ["/api/v1/health", "/proxy/", "/api/v1/", "/.env"], "tech_stack": ["<PERSON><PERSON>", "vue.js", "gin", "golang", "Go"], "ports": ["3001"]}, "search_strategies": {"basic": ["port=\"3001\"", "title*=\"GPT-Load\"", "body*=\"gpt-load\""], "precise": ["title=\"GPT-Load\" || title=\"gpt-load\"", "body*=\"tbphp/gpt-load\"", "body*=\"智能密钥轮询的多渠道 AI 代理\"", "body*=\"Multi-channel AI proxy with intelligent key rotation\""], "technical": ["port=\"3001\" && body*=\"Vue\" && body*=\"proxy\"", "server*=\"Go\" && body*=\"AI\" && body*=\"proxy\"", "body*=\"gin\" && body*=\"openai\" && port=\"3001\""], "api_focused": ["path=\"/api/v1/health\" && port=\"3001\"", "path=\"/proxy/\" && body*=\"AI\"", "body*=\"/proxy/\" && body*=\"chat/completions\""], "combined": ["port=\"3001\" && (title*=\"GPT\" || body*=\"gpt-load\")", "(title*=\"GPT-Load\" || body*=\"gpt-load\") && country=\"CN\"", "body*=\"proxy\" && body*=\"openai\" && body*=\"gemini\" && body*=\"claude\""]}}