# 服务器配置
PORT=3001
HOST=0.0.0.0

# 服务器读取、写入和空闲连接的超时时间（秒）
SERVER_READ_TIMEOUT=60
SERVER_WRITE_TIMEOUT=600
SERVER_IDLE_TIMEOUT=120
SERVER_GRACEFUL_SHUTDOWN_TIMEOUT=10

# 从节点标识
IS_SLAVE=false

# 时区
TZ=Asia/Shanghai

# 认证配置 是必需的，用于保护管理 API 和 UI 界面
AUTH_KEY=sk-123456

# 数据库配置 默认不填写，使用./data/gpt-load.db的SQLite
# MySQL 示例:
# DATABASE_DSN=root:123456@tcp(mysql:3306)/gpt-load?charset=utf8mb4&parseTime=True&loc=Local
# PostgreSQL 示例:
# DATABASE_DSN=****************************************/gpt-load?sslmode=disable

# Redis配置 默认不填写，使用内存存储
# REDIS_DSN=redis://redis:6379/0

# 并发数量
MAX_CONCURRENT_REQUESTS=100

# CORS配置
ENABLE_CORS=true
ALLOWED_ORIGINS=*
ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
ALLOWED_HEADERS=*
ALLOW_CREDENTIALS=false

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=text
LOG_ENABLE_FILE=true
LOG_FILE_PATH=./data/logs/app.log
