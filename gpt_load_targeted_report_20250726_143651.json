{"timestamp": "2025-07-26T14:36:51.819509", "summary": {"total_queries": 14, "total_results": 19247669, "verified_sites": 11}, "verified_sites": [{"url": "http://***************:3001", "host": "***************:3001", "title": "GPT Load", "country": "SG", "score": 15, "features": ["title:GP<PERSON>", "cors_signature", "content_length_match", "status_200", "health_404"], "query": "title=\"GPT Load\""}, {"url": "http://api.lty.bio", "host": "api.lty.bio", "title": "GPT Load", "country": "DE", "score": 15, "features": ["title:GP<PERSON>", "cors_signature", "content_length_match", "status_200", "health_404"], "query": "title=\"GPT Load\""}, {"url": "http://**************:3001", "host": "**************:3001", "title": "GPT Load", "country": "US", "score": 15, "features": ["title:GP<PERSON>", "cors_signature", "content_length_match", "status_200", "health_404"], "query": "title=\"GPT Load\""}, {"url": "http://gpt-load.110086.xyz", "host": "gpt-load.110086.xyz", "title": "GPT Load", "country": "US", "score": 15, "features": ["title:GP<PERSON>", "cors_signature", "content_length_match", "status_200", "health_404"], "query": "title=\"GPT Load\""}, {"url": "http://load.8388608.xyz", "host": "load.8388608.xyz", "title": "GPT Load", "country": "SG", "score": 15, "features": ["title:GP<PERSON>", "cors_signature", "content_length_match", "status_200", "health_404"], "query": "title=\"GPT Load\""}, {"url": "http://**************:3001", "host": "**************:3001", "title": "GPT Load", "country": "CN", "score": 15, "features": ["title:GP<PERSON>", "cors_signature", "content_length_match", "status_200", "health_404"], "query": "port=\"3001\" && title=\"GPT\""}, {"url": "http://*************:3001", "host": "*************:3001", "title": "GPT Load", "country": "US", "score": 15, "features": ["title:GP<PERSON>", "cors_signature", "content_length_match", "status_200", "health_404"], "query": "port=\"3001\" && title=\"GPT\""}, {"url": "http://**************:3001", "host": "**************:3001", "title": "GPT Load", "country": "CN", "score": 15, "features": ["title:GP<PERSON>", "cors_signature", "content_length_match", "status_200", "health_404"], "query": "port=\"3001\" && title=\"GPT\""}, {"url": "http://**************:3001", "host": "**************:3001", "title": "GPT Load", "country": "CN", "score": 15, "features": ["title:GP<PERSON>", "cors_signature", "content_length_match", "status_200", "health_404"], "query": "port=\"3001\" && title=\"Load\""}, {"url": "http://**************:3001", "host": "**************:3001", "title": "", "country": "IN", "score": 7, "features": ["cors_signature", "health_404"], "query": "server=\"Go\" && port=\"3001\""}, {"url": "http://**************:3001", "host": "**************:3001", "title": "word-gpt-plus", "country": "CN", "score": 8, "features": ["cors_signature", "status_200", "health_404"], "query": "port=\"3001\" && country=\"CN\" && title=\"GPT\""}], "effective_queries": [["title=\"GPT Load\"", 5], ["port=\"3001\" && title=\"GPT\"", 3], ["port=\"3001\" && title=\"Load\"", 1], ["server=\"Go\" && port=\"3001\"", 1], ["port=\"3001\" && country=\"CN\" && title=\"GPT\"", 1]]}