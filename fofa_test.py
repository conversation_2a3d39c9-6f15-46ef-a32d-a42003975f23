#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPT-Load FOFA搜索测试脚本 - 简化版
"""

import requests
import json
import base64
import time
from datetime import datetime

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ 配置文件config.json不存在")
        return None

def fofa_search(config, query, size=50):
    """执行FOFA搜索"""
    fofa_config = config['fofa']
    
    try:
        # Base64编码查询
        encoded_query = base64.b64encode(query.encode()).decode()
        
        # 构建请求
        api_url = f"{fofa_config['url']}/api/v1/search/all"
        params = {
            'key': fofa_config['api_key'],
            'qbase64': encoded_query,
            'size': size,
            'fields': 'host,title,ip,port,protocol,country,server'
        }
        
        response = requests.get(api_url, params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('error'):
                return {'success': False, 'error': data.get('errmsg')}
            
            return {
                'success': True,
                'query': query,
                'total': data.get('size', 0),
                'results': data.get('results', [])
            }
        else:
            return {'success': False, 'error': f'HTTP {response.status_code}'}
            
    except Exception as e:
        return {'success': False, 'error': str(e)}

def test_search_strategies(config):
    """测试所有搜索策略"""
    strategies = config['search_strategies']
    results = []
    
    print("🚀 开始测试GPT-Load FOFA搜索策略")
    print("=" * 60)
    
    for strategy_name, queries in strategies.items():
        print(f"\n📋 策略: {strategy_name}")
        print("-" * 40)
        
        for query in queries:
            print(f"🔍 测试: {query}")
            
            result = fofa_search(config, query)
            
            if result['success']:
                total = result['total']
                print(f"✅ 找到 {total} 个结果")
                results.append({
                    'strategy': strategy_name,
                    'query': query,
                    'total': total,
                    'timestamp': datetime.now().isoformat()
                })
                
                # 显示前3个结果
                if total > 0:
                    for i, res in enumerate(result['results'][:3], 1):
                        host = res[0] if len(res) > 0 else 'N/A'
                        title = res[1] if len(res) > 1 else 'N/A'
                        country = res[5] if len(res) > 5 else 'N/A'
                        print(f"   {i}. {host} | {country} | {title[:50]}...")
            else:
                print(f"❌ 搜索失败: {result['error']}")
            
            # 延迟避免请求过快
            time.sleep(config['fofa']['request_delay'])
    
    return results

def generate_report(results):
    """生成搜索报告"""
    if not results:
        print("\n❌ 没有有效的搜索结果")
        return
    
    print("\n" + "=" * 60)
    print("📊 搜索效果报告")
    print("=" * 60)
    
    # 按结果数量排序
    sorted_results = sorted(results, key=lambda x: x['total'], reverse=True)
    
    print(f"{'排名':<4} {'结果数':<8} {'策略':<15} {'搜索语法'}")
    print("-" * 80)
    
    for i, result in enumerate(sorted_results[:10], 1):
        strategy = result['strategy'][:14]
        total = result['total']
        query = result['query'][:45] + '...' if len(result['query']) > 45 else result['query']
        print(f"{i:<4} {total:<8} {strategy:<15} {query}")
    
    # 推荐最佳语法
    if sorted_results:
        best = sorted_results[0]
        print(f"\n🏆 推荐搜索语法:")
        print(f"   {best['query']}")
        print(f"   策略: {best['strategy']}")
        print(f"   结果数: {best['total']}")
        
        # 保存结果到文件
        with open('fofa_results.json', 'w', encoding='utf-8') as f:
            json.dump(sorted_results, f, ensure_ascii=False, indent=2)
        print(f"\n💾 详细结果已保存到 fofa_results.json")

def quick_test():
    """快速测试最有效的几个搜索语法"""
    config = load_config()
    if not config:
        return
    
    # 精选高效搜索语法
    quick_queries = [
        'title="GPT-Load" || title="gpt-load"',
        'body*="tbphp/gpt-load"',
        'port="3001" && body*="proxy" && body*="AI"',
        'body*="智能密钥轮询"',
        '(title*="GPT-Load" || body*="gpt-load") && country="CN"'
    ]
    
    print("⚡ 快速测试模式")
    print("=" * 40)
    
    results = []
    for query in quick_queries:
        print(f"\n🔍 {query}")
        result = fofa_search(config, query, size=20)
        
        if result['success']:
            total = result['total']
            print(f"✅ {total} 个结果")
            results.append({
                'query': query,
                'total': total,
                'results': result['results'][:3]  # 保存前3个结果
            })
        else:
            print(f"❌ {result['error']}")
        
        time.sleep(1)
    
    # 显示最佳结果
    if results:
        best = max(results, key=lambda x: x['total'])
        print(f"\n🎯 最佳语法: {best['query']}")
        print(f"📈 结果数量: {best['total']}")
        
        print(f"\n📋 示例结果:")
        for i, res in enumerate(best['results'], 1):
            if len(res) > 0:
                host = res[0]
                title = res[1] if len(res) > 1 else 'N/A'
                print(f"   {i}. {host}")
                print(f"      标题: {title}")

def main():
    """主函数"""
    config = load_config()
    if not config:
        return
    
    print("选择运行模式:")
    print("1. 完整测试 (测试所有策略)")
    print("2. 快速测试 (测试精选语法)")
    
    choice = input("\n请选择 (1/2): ").strip()
    
    if choice == '1':
        results = test_search_strategies(config)
        generate_report(results)
    elif choice == '2':
        quick_test()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
