#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPT-Load FOFA全面搜索策略
基于深度分析的多维度搜索方法
"""

import requests
import json
import base64
import time
from datetime import datetime
import concurrent.futures
from urllib.parse import urlparse

class ComprehensiveFOFAScanner:
    def __init__(self, fofa_url="https://fofa.red", api_key="1v43heo2ie8004lp"):
        self.fofa_url = fofa_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.verified_sites = []
        self.all_results = []
    
    def search(self, query, size=100):
        """执行FOFA搜索"""
        try:
            encoded_query = base64.b64encode(query.encode()).decode()
            
            api_url = f"{self.fofa_url}/api/v1/search/all"
            params = {
                'key': self.api_key,
                'qbase64': encoded_query,
                'size': size,
                'fields': 'host,title,ip,port,protocol,country,region,city,server,banner,header'
            }
            
            response = self.session.get(api_url, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('error'):
                    return {'success': False, 'error': data.get('errmsg')}
                
                return {
                    'success': True,
                    'query': query,
                    'total': data.get('size', 0),
                    'results': data.get('results', [])
                }
            else:
                return {'success': False, 'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def verify_gpt_load_advanced(self, url):
        """高级GPT-Load验证"""
        try:
            # 检查主页
            response = self.session.get(url, timeout=10, allow_redirects=True)
            content = response.text.lower()
            headers = {k.lower(): v for k, v in response.headers.items()}
            
            # 多维度特征检测
            features = {
                # 标题特征
                'title_gpt_load': any(title in content for title in [
                    'gpt-load', 'gpt load', 'ai代理', '智能密钥'
                ]),
                
                # API路径特征
                'proxy_api': '/proxy/' in content or 'proxy' in content,
                'api_v1': '/api/v1' in content,
                
                # AI服务特征
                'ai_services': any(service in content for service in [
                    'openai', 'gemini', 'claude', 'anthropic', 'chat/completions'
                ]),
                
                # 技术栈特征
                'vue_app': any(vue in content for vue in [
                    'vue.js', '__vue__', 'vue-router', 'element-plus', 'element-ui'
                ]),
                'go_server': 'go' in headers.get('server', '').lower(),
                
                # 管理界面特征
                'management_ui': any(ui in content for ui in [
                    '管理', 'dashboard', 'api key', '密钥管理', '仪表盘'
                ]),
                
                # 认证特征
                'auth_required': any(auth in content for auth in [
                    'authorization', 'auth_key', 'sk-', 'bearer'
                ]),
                
                # 中文描述特征
                'chinese_desc': any(desc in response.text for desc in [
                    '智能密钥轮询', 'AI代理', '多渠道', '透明代理', '负载均衡'
                ])
            }
            
            # 检查特定API端点
            api_checks = self._check_api_endpoints(url)
            features.update(api_checks)
            
            # 计算置信度
            score = sum(features.values())
            confidence = score / len(features) * 100
            
            # 判断是否为GPT-Load
            is_gpt_load = score >= 3  # 至少匹配3个特征
            
            return is_gpt_load, confidence, features
            
        except Exception as e:
            return False, 0, {'error': str(e)}
    
    def _check_api_endpoints(self, base_url):
        """检查API端点"""
        endpoints = [
            '/api/v1/health',
            '/proxy/',
            '/api/v1/',
            '/.env',
            '/health'
        ]
        
        features = {}
        for endpoint in endpoints:
            try:
                url = f"{base_url.rstrip('/')}{endpoint}"
                resp = self.session.get(url, timeout=5)
                
                # 根据响应判断
                if endpoint == '/api/v1/health':
                    features['health_endpoint'] = resp.status_code in [200, 401, 403]
                elif endpoint == '/proxy/':
                    features['proxy_endpoint'] = resp.status_code in [200, 401, 403, 404]
                elif endpoint == '/.env':
                    features['env_exposed'] = 'AUTH_KEY' in resp.text or 'DATABASE_DSN' in resp.text
                
            except:
                continue
        
        return features

def get_comprehensive_search_strategies():
    """获取全面的搜索策略"""
    return {
        # 1. 精确标题搜索
        'exact_titles': [
            'title="GPT-Load"',
            'title="gpt-load"',
            'title="GPT Load"',
            'title="AI代理"',
            'title="智能密钥"'
        ],
        
        # 2. 端口+地区组合
        'port_location': [
            'port="3001" && country="CN"',
            'port="3001" && country="US"',
            'port="3001" && region="Beijing"',
            'port="3001" && region="Shanghai"',
            'port="3001" && region="Guangdong"',
            'port="3001" && region="Zhejiang"'
        ],
        
        # 3. 技术栈特征
        'tech_stack': [
            'server="Go" && port="3001"',
            'header="gin"',
            'server="gin"',
            'banner="Go"'
        ],
        
        # 4. API路径特征
        'api_paths': [
            'path="/proxy"',
            'path="/api/v1"',
            'path="/health"',
            'path="/api/v1/health"'
        ],
        
        # 5. 错误页面特征
        'error_pages': [
            'body="Authorization required"',
            'body="Invalid API key"',
            'body="Unauthorized"',
            'body="401"'
        ],
        
        # 6. 静态资源特征
        'static_resources': [
            'body="vue.js"',
            'body="element-plus"',
            'body="element-ui"',
            'body="__vue__"'
        ],
        
        # 7. 配置泄露
        'config_exposure': [
            'body="AUTH_KEY"',
            'body="DATABASE_DSN"',
            'body="sk-123456"',
            'path="/.env"'
        ],
        
        # 8. 组合搜索
        'combined_searches': [
            'title="GPT-Load" || title="gpt-load"',
            'port="3001" && (title="GPT" || title="AI")',
            'server="Go" && (body="proxy" || body="API")',
            'path="/proxy" && port="3001"',
            'body="openai" && body="gemini" && port="3001"',
            '(title="Load" || body="load") && port="3001"'
        ],
        
        # 9. 反向代理部署
        'reverse_proxy': [
            'body="proxy" && body="openai" && port="80"',
            'body="proxy" && body="openai" && port="443"',
            'body="AI" && body="代理" && port="80"',
            'body="AI" && body="代理" && port="443"'
        ],
        
        # 10. Docker部署特征
        'docker_deployment': [
            'banner="Docker"',
            'server="nginx" && body="proxy"',
            'header="X-Forwarded-For"'
        ]
    }

def run_comprehensive_scan():
    """运行全面扫描"""
    scanner = ComprehensiveFOFAScanner()
    strategies = get_comprehensive_search_strategies()
    
    print("🚀 GPT-Load 全面搜索扫描")
    print("=" * 60)
    
    all_results = []
    unique_hosts = set()
    
    for strategy_name, queries in strategies.items():
        print(f"\n📋 策略: {strategy_name}")
        print("-" * 40)
        
        for query in queries:
            print(f"🔍 {query}")
            
            result = scanner.search(query, size=50)
            
            if result and result['success']:
                total = result['total']
                print(f"✅ {total} 个结果")
                
                if total > 0:
                    all_results.append(result)
                    
                    # 收集唯一主机
                    for res in result['results']:
                        if len(res) > 0:
                            host = res[0]
                            unique_hosts.add(host)
                    
                    # 显示前3个结果
                    for i, res in enumerate(result['results'][:3], 1):
                        if len(res) > 0:
                            host = res[0]
                            title = res[1] if len(res) > 1 else 'N/A'
                            country = res[5] if len(res) > 5 else 'N/A'
                            print(f"   {i}. {host} | {country} | {title[:30]}...")
            else:
                error_msg = result.get('error', '未知错误') if result else '搜索失败'
                print(f"❌ {error_msg}")
            
            time.sleep(1.5)  # 避免请求过快
    
    print(f"\n📊 搜索汇总:")
    print(f"  总查询数: {len([r for r in all_results if r['total'] > 0])}")
    print(f"  总结果数: {sum(r['total'] for r in all_results)}")
    print(f"  唯一主机: {len(unique_hosts)}")
    
    return all_results, list(unique_hosts)

def verify_unique_hosts(scanner, unique_hosts):
    """验证唯一主机"""
    print(f"\n🔍 验证唯一主机 ({len(unique_hosts)} 个)")
    print("-" * 40)
    
    verified_sites = []
    
    # 并发验证以提高效率
    def verify_host(host):
        try:
            # 尝试HTTP和HTTPS
            for protocol in ['http', 'https']:
                url = f"{protocol}://{host}"
                is_gpt_load, confidence, features = scanner.verify_gpt_load_advanced(url)
                
                if is_gpt_load:
                    return {
                        'url': url,
                        'host': host,
                        'confidence': confidence,
                        'features': features,
                        'verified': True
                    }
            
            return {'host': host, 'verified': False}
            
        except Exception as e:
            return {'host': host, 'verified': False, 'error': str(e)}
    
    # 限制并发数避免被封
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        futures = {executor.submit(verify_host, host): host for host in unique_hosts[:20]}  # 只验证前20个
        
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            
            if result.get('verified'):
                verified_sites.append(result)
                print(f"✅ {result['url']} (置信度: {result['confidence']:.1f}%)")
            else:
                print(f"❌ {result['host']}")
            
            time.sleep(0.5)
    
    return verified_sites

def generate_comprehensive_report(all_results, verified_sites):
    """生成全面报告"""
    print("\n" + "=" * 60)
    print("📈 GPT-Load 全面搜索报告")
    print("=" * 60)
    
    # 统计信息
    total_queries = len(all_results)
    total_results = sum(r['total'] for r in all_results)
    verified_count = len(verified_sites)
    
    print(f"\n📊 统计概览:")
    print(f"  执行查询: {total_queries}")
    print(f"  搜索结果: {total_results}")
    print(f"  已验证站点: {verified_count}")
    
    # 最佳搜索语法
    if all_results:
        sorted_results = sorted(all_results, key=lambda x: x['total'], reverse=True)
        
        print(f"\n🏆 最佳搜索语法:")
        for i, result in enumerate(sorted_results[:10], 1):
            print(f"  {i:2d}. {result['query']} ({result['total']} 个结果)")
    
    # 已验证站点
    if verified_sites:
        print(f"\n✅ 已确认的GPT-Load站点:")
        for i, site in enumerate(verified_sites, 1):
            print(f"  {i}. {site['url']}")
            print(f"     置信度: {site['confidence']:.1f}%")
            matched_features = [k for k, v in site['features'].items() if v and k != 'error']
            print(f"     特征: {', '.join(matched_features[:5])}")
            print()
    
    # 搜索建议
    print(f"\n💡 优化建议:")
    
    if verified_count > 0:
        # 分析成功的搜索模式
        successful_queries = []
        for result in sorted_results:
            if result['total'] > 0 and result['total'] < 10000:  # 结果数适中
                successful_queries.append(result['query'])
        
        print(f"  🎯 推荐搜索语法:")
        for query in successful_queries[:5]:
            print(f"    {query}")
    
    print(f"\n  🔄 搜索策略建议:")
    print(f"    1. 使用多个搜索语法组合")
    print(f"    2. 关注端口3001 + 地区过滤")
    print(f"    3. 检查API路径特征")
    print(f"    4. 验证技术栈特征")
    print(f"    5. 考虑反向代理部署")
    
    # 保存报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total_queries': total_queries,
            'total_results': total_results,
            'verified_sites': verified_count
        },
        'search_results': all_results,
        'verified_sites': verified_sites,
        'top_queries': [r['query'] for r in sorted_results[:10]]
    }
    
    filename = f"gpt_load_comprehensive_report_{timestamp}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 完整报告已保存: {filename}")

def main():
    """主函数"""
    print("🎯 GPT-Load 全面搜索工具")
    print("基于多维度特征的深度搜索")
    
    # 运行全面扫描
    all_results, unique_hosts = run_comprehensive_scan()
    
    # 验证唯一主机
    scanner = ComprehensiveFOFAScanner()
    verified_sites = verify_unique_hosts(scanner, unique_hosts)
    
    # 生成报告
    generate_comprehensive_report(all_results, verified_sites)

if __name__ == "__main__":
    main()
